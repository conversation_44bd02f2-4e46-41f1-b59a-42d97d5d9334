# Mobile-Agent-E 本地配置文件
# 请填入你的真实API密钥

# ADB路径配置
$env:ADB_PATH = "C:\apps\adb\platform-tools\adb.exe"  # Windows默认路径
# $env:ADB_PATH = "adb"  # 如果adb在PATH中，可以使用这个

# 选择后端模型类型
$env:BACKBONE_TYPE = "Qwen"  # 可选: "OpenAI", "Gemini", "Claude", "Qwen"

# === API密钥配置 (必须设置) ===
# 请将 "your-xxx-api-key-here" 替换为你的真实API密钥

# Qwen相关API密钥 (必需) - 通常这三个可以使用同一个阿里云DashScope API密钥
$env:QWEN_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"               # 用于图标识别
$env:QWEN_REASONING_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"     # 用于推理模型
$env:DASHSCOPE_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"          # DashScope API

# 注意：上面三个密钥通常是同一个阿里云DashScope API密钥
# 如果您只有一个密钥，可以将同一个密钥填入上面三个位置

# 其他模型API密钥 (可选，根据BACKBONE_TYPE选择)
# $env:OPENAI_API_KEY = "your-openai-api-key-here"
# $env:GEMINI_API_KEY = "your-gemini-api-key-here" 
# $env:CLAUDE_API_KEY = "your-claude-api-key-here"

# 显示配置状态
Write-Host "=== Mobile-Agent-E 配置加载 ===" -ForegroundColor Green
Write-Host "ADB_PATH: $env:ADB_PATH"
Write-Host "BACKBONE_TYPE: $env:BACKBONE_TYPE"

# 检查必需的API密钥
$missing_keys = @()
if (-not $env:QWEN_API_KEY -or $env:QWEN_API_KEY -eq "your-dashscope-api-key-here") {
    $missing_keys += "QWEN_API_KEY"
}
if (-not $env:QWEN_REASONING_API_KEY -or $env:QWEN_REASONING_API_KEY -eq "your-dashscope-api-key-here") {
    $missing_keys += "QWEN_REASONING_API_KEY"
}
if (-not $env:DASHSCOPE_API_KEY -or $env:DASHSCOPE_API_KEY -eq "your-dashscope-api-key-here") {
    $missing_keys += "DASHSCOPE_API_KEY"
}

if ($missing_keys.Count -eq 0) {
    Write-Host "✅ 所有必需的API密钥已设置" -ForegroundColor Green
    Write-Host "可以开始运行任务！" -ForegroundColor Green
} else {
    Write-Host "❌ 缺少以下API密钥:" -ForegroundColor Red
    foreach ($key in $missing_keys) {
        Write-Host "  - $key" -ForegroundColor Red
    }
    Write-Host "请在配置文件中设置这些密钥后再运行任务" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "使用方法:"
Write-Host "1. 获取阿里云DashScope API密钥: https://help.aliyun.com/document_detail/2712195.html"
Write-Host "2. 编辑此文件，将上面三个位置都填入同一个API密钥"
Write-Host "3. 运行: . .\config_local.ps1"
Write-Host "4. 运行任务: python run.py --instruction '你的指令' --run_name 'test'"
